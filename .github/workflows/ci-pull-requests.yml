name: Build, unit test and lint branch

on:
  pull_request:
    branches:
      - '**'
      - '!release/*'

jobs:
  install-and-build:
    name: Install & Build
    runs-on: blacksmith-2vcpu-ubuntu-2204
    env:
      NODE_OPTIONS: '--max-old-space-size=4096'
    steps:
      - uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1
        with:
          ref: refs/pull/${{ github.event.pull_request.number }}/merge

      - name: Setup Environment and Build Project
        uses: ./.github/actions/setup-and-build
        with:
          node-version: 22.x
          enable-caching: true

      - name: Run formatcheck
        run: pnpm format:check

      - name: Run typecheck
        run: pnpm typecheck

  unit-test:
    name: Unit tests
    uses: ./.github/workflows/units-tests-reusable.yml
    needs: install-and-build
    with:
      ref: refs/pull/${{ github.event.pull_request.number }}/merge
      cacheKey: ${{ github.sha }}-base:build
      collectCoverage: true
    secrets:
      CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}

  lint:
    name: <PERSON>t
    uses: ./.github/workflows/linting-reusable.yml
    needs: install-and-build
    with:
      ref: refs/pull/${{ github.event.pull_request.number }}/merge
      cacheKey: ${{ github.sha }}-base:build
