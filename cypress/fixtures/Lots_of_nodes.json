{"name": "Lots of nodes", "nodes": [{"parameters": {}, "id": "369fe424-dd3b-4399-9de3-50bd4ce1f75b", "name": "When clicking ‘Execute workflow’", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [860, 740]}, {"parameters": {"jsCode": "// Loop over input items and add a new field called 'myNewField' to the JSON of each one\nfor (const item of $input.all()) {\n  item.json.myNewField = 1;\n}\n\nreturn $input.all();"}, "id": "dce967a7-8c5e-43cc-ba2b-e0fb0c9cf14c", "name": "Code", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1080, 740]}, {"parameters": {"options": {}}, "id": "df7a719e-b25a-43e3-b941-7091a7d9a1a8", "name": "<PERSON>", "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [1300, 740]}, {"parameters": {}, "id": "32968b79-6a8b-43ed-b884-eb906b597661", "name": "IF", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1520, 740]}, {"parameters": {"jsCode": "// Loop over input items and add a new field called 'myNewField' to the JSON of each one\nfor (const item of $input.all()) {\n  item.json.myNewField = 1;\n}\n\nreturn $input.all();"}, "id": "e9a72745-6dbb-4be1-b286-aaa679b95e36", "name": "Code1", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1820, 80]}, {"parameters": {"options": {}}, "id": "f831d21b-c3a9-4bd8-9fc3-6daef12bd43f", "name": "Edit Fields1", "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [2040, 80]}, {"parameters": {}, "id": "6e6b2a4f-9e61-4245-8502-ca01e851fcbe", "name": "IF1", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [2260, 80]}, {"parameters": {"jsCode": "// Loop over input items and add a new field called 'myNewField' to the JSON of each one\nfor (const item of $input.all()) {\n  item.json.myNewField = 1;\n}\n\nreturn $input.all();"}, "id": "535b9786-ead9-44f9-bff2-ef2e019a4cf9", "name": "Code3", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2560, -260]}, {"parameters": {"options": {}}, "id": "6a181d75-f2f2-4ad1-be3c-81ebe077ccc8", "name": "Edit Fields3", "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [2780, -260]}, {"parameters": {}, "id": "4b45828e-4e2b-4046-b9ae-24b373a81863", "name": "IF7", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [3000, -260]}, {"parameters": {"jsCode": "// Loop over input items and add a new field called 'myNewField' to the JSON of each one\nfor (const item of $input.all()) {\n  item.json.myNewField = 1;\n}\n\nreturn $input.all();"}, "id": "059534cb-820c-4fb7-933c-eeed2ae74f1c", "name": "Code7", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [3260, -400]}, {"parameters": {"options": {}}, "id": "4f5c0d94-b69d-4ad3-aa8f-f1dd5824ec4a", "name": "Edit Fields7", "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [3480, -400]}, {"parameters": {}, "id": "cd74f840-7b0f-425d-8ecd-e247a7d8abf5", "name": "IF8", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [3700, -400]}, {"parameters": {"jsCode": "// Loop over input items and add a new field called 'myNewField' to the JSON of each one\nfor (const item of $input.all()) {\n  item.json.myNewField = 1;\n}\n\nreturn $input.all();"}, "id": "3c97fd14-9c23-45e2-a1ac-934d743e9a01", "name": "Code8", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [3260, -80]}, {"parameters": {"options": {}}, "id": "9e7bd7e9-5142-4751-b132-735d27007d82", "name": "Edit Fields8", "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [3480, -80]}, {"parameters": {}, "id": "8d3968b6-16d4-4e03-9026-eeaf70b17805", "name": "IF9", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [3700, -80]}, {"parameters": {"jsCode": "// Loop over input items and add a new field called 'myNewField' to the JSON of each one\nfor (const item of $input.all()) {\n  item.json.myNewField = 1;\n}\n\nreturn $input.all();"}, "id": "141edef3-ea0f-4e90-9b6a-09f5d5551195", "name": "Code4", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2560, 440]}, {"parameters": {"options": {}}, "id": "b5b93cd7-9448-4290-91b7-c3c8429925fd", "name": "Edit Fields4", "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [2780, 440]}, {"parameters": {}, "id": "79d2c11c-0378-4ff5-b166-ae1bf773f53a", "name": "IF14", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [3000, 440]}, {"parameters": {"jsCode": "// Loop over input items and add a new field called 'myNewField' to the JSON of each one\nfor (const item of $input.all()) {\n  item.json.myNewField = 1;\n}\n\nreturn $input.all();"}, "id": "8483e962-24e7-4495-9c8e-481481ebe897", "name": "Code13", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [3260, 300]}, {"parameters": {"options": {}}, "id": "74dfb8f9-6d14-493e-97d5-729e1f44856b", "name": "Edit Fields13", "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [3480, 300]}, {"parameters": {}, "id": "0c2e8e54-958d-4932-91b5-b23979460c97", "name": "IF15", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [3700, 300]}, {"parameters": {"jsCode": "// Loop over input items and add a new field called 'myNewField' to the JSON of each one\nfor (const item of $input.all()) {\n  item.json.myNewField = 1;\n}\n\nreturn $input.all();"}, "id": "bfed29c6-c453-4850-8acf-7aa11b1d0d8e", "name": "Code14", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [3260, 620]}, {"parameters": {"options": {}}, "id": "d8415057-c597-40a9-95f6-bafbe3fafac0", "name": "Edit Fields14", "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [3480, 620]}, {"parameters": {}, "id": "51ed9040-bb6c-4f77-9740-74b54ac56a00", "name": "IF16", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [3700, 620]}, {"parameters": {"jsCode": "// Loop over input items and add a new field called 'myNewField' to the JSON of each one\nfor (const item of $input.all()) {\n  item.json.myNewField = 1;\n}\n\nreturn $input.all();"}, "id": "5864e701-eb16-4412-ae8b-be1f2a1f16a5", "name": "Code2", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1820, 1480]}, {"parameters": {"options": {}}, "id": "4b7de291-f1c7-4ae8-a545-81aaa2ebd1fb", "name": "Edit Fields2", "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [2040, 1480]}, {"parameters": {}, "id": "328aa16f-82ed-465e-b548-9436f21eb519", "name": "IF2", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [2260, 1480]}, {"parameters": {"jsCode": "// Loop over input items and add a new field called 'myNewField' to the JSON of each one\nfor (const item of $input.all()) {\n  item.json.myNewField = 1;\n}\n\nreturn $input.all();"}, "id": "90aaf0b0-57b6-4a08-b000-abb2956ba640", "name": "Code5", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2560, 1140]}, {"parameters": {"options": {}}, "id": "7d327c87-da3b-4f4b-9f9a-51c9c622990d", "name": "Edit Fields5", "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [2780, 1140]}, {"parameters": {}, "id": "fa2a3b1b-53de-454e-a16d-e2bf62cb05ec", "name": "IF21", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [3000, 1140]}, {"parameters": {"jsCode": "// Loop over input items and add a new field called 'myNewField' to the JSON of each one\nfor (const item of $input.all()) {\n  item.json.myNewField = 1;\n}\n\nreturn $input.all();"}, "id": "8efaa5a3-982e-41b4-af6e-28e35c64093d", "name": "Code19", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [3260, 1000]}, {"parameters": {"options": {}}, "id": "987e27fd-778a-4562-85a9-369b1ec232de", "name": "Edit Fields19", "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [3480, 1000]}, {"parameters": {}, "id": "b3f4e9b3-9995-4019-9b0f-dadd64e036b4", "name": "IF22", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [3700, 1000]}, {"parameters": {"jsCode": "// Loop over input items and add a new field called 'myNewField' to the JSON of each one\nfor (const item of $input.all()) {\n  item.json.myNewField = 1;\n}\n\nreturn $input.all();"}, "id": "681c1b30-063d-4c1e-b550-942a9dd3eb9a", "name": "Code20", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [3260, 1320]}, {"parameters": {"options": {}}, "id": "024770b6-7bf4-44f6-9675-d4f7dc73d6ac", "name": "Edit Fields20", "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [3480, 1320]}, {"parameters": {}, "id": "24699015-3ccf-4ffa-b52f-8ba4c4853963", "name": "IF23", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [3700, 1320]}, {"parameters": {"jsCode": "// Loop over input items and add a new field called 'myNewField' to the JSON of each one\nfor (const item of $input.all()) {\n  item.json.myNewField = 1;\n}\n\nreturn $input.all();"}, "id": "f4b2d116-2fda-4a3a-9509-0e8c64e7796e", "name": "Code6", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2560, 1840]}, {"parameters": {"options": {}}, "id": "535e5b12-6743-4c01-9fc5-e27b10421423", "name": "Edit Fields6", "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [2780, 1840]}, {"parameters": {}, "id": "3dcbecdf-686b-445f-9c77-2902d0dc1f56", "name": "IF28", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [3000, 1840]}, {"parameters": {"jsCode": "// Loop over input items and add a new field called 'myNewField' to the JSON of each one\nfor (const item of $input.all()) {\n  item.json.myNewField = 1;\n}\n\nreturn $input.all();"}, "id": "7223c6ef-664b-426a-8d08-eca1b34e6b23", "name": "Code25", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [3260, 1700]}, {"parameters": {"options": {}}, "id": "496414a6-384a-4f94-97ec-d2e5ad646f82", "name": "Edit Fields25", "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [3480, 1700]}, {"parameters": {}, "id": "82f9562d-e4a8-49f3-924d-983effb4b6c6", "name": "IF29", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [3700, 1700]}, {"parameters": {"jsCode": "// Loop over input items and add a new field called 'myNewField' to the JSON of each one\nfor (const item of $input.all()) {\n  item.json.myNewField = 1;\n}\n\nreturn $input.all();"}, "id": "c91d4bc5-3c60-4c22-aa31-44e84e0816ec", "name": "Code26", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [3260, 2020]}, {"parameters": {"options": {}}, "id": "49b61f23-bf3f-474d-8bba-a3b7de6f6441", "name": "Edit Fields26", "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [3480, 2020]}, {"parameters": {}, "id": "1cad6ae3-1064-4f30-a9ec-502891868332", "name": "IF30", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [3700, 2020]}], "pinData": {}, "connections": {"When clicking ‘Execute workflow’": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}, "IF": {"main": [[{"node": "Code1", "type": "main", "index": 0}], [{"node": "Code2", "type": "main", "index": 0}]]}, "Code1": {"main": [[{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "Edit Fields1": {"main": [[{"node": "IF1", "type": "main", "index": 0}]]}, "Code3": {"main": [[{"node": "Edit Fields3", "type": "main", "index": 0}]]}, "Edit Fields3": {"main": [[{"node": "IF7", "type": "main", "index": 0}]]}, "IF1": {"main": [[{"node": "Code3", "type": "main", "index": 0}], [{"node": "Code4", "type": "main", "index": 0}]]}, "IF7": {"main": [[{"node": "Code7", "type": "main", "index": 0}], [{"node": "Code8", "type": "main", "index": 0}]]}, "Code7": {"main": [[{"node": "Edit Fields7", "type": "main", "index": 0}]]}, "Edit Fields7": {"main": [[{"node": "IF8", "type": "main", "index": 0}]]}, "Code8": {"main": [[{"node": "Edit Fields8", "type": "main", "index": 0}]]}, "Edit Fields8": {"main": [[{"node": "IF9", "type": "main", "index": 0}]]}, "Code4": {"main": [[{"node": "Edit Fields4", "type": "main", "index": 0}]]}, "Edit Fields4": {"main": [[{"node": "IF14", "type": "main", "index": 0}]]}, "IF14": {"main": [[{"node": "Code13", "type": "main", "index": 0}], [{"node": "Code14", "type": "main", "index": 0}]]}, "Code13": {"main": [[{"node": "Edit Fields13", "type": "main", "index": 0}]]}, "Edit Fields13": {"main": [[{"node": "IF15", "type": "main", "index": 0}]]}, "Code14": {"main": [[{"node": "Edit Fields14", "type": "main", "index": 0}]]}, "Edit Fields14": {"main": [[{"node": "IF16", "type": "main", "index": 0}]]}, "Code2": {"main": [[{"node": "Edit Fields2", "type": "main", "index": 0}]]}, "Edit Fields2": {"main": [[{"node": "IF2", "type": "main", "index": 0}]]}, "IF2": {"main": [[{"node": "Code5", "type": "main", "index": 0}], [{"node": "Code6", "type": "main", "index": 0}]]}, "Code5": {"main": [[{"node": "Edit Fields5", "type": "main", "index": 0}]]}, "Edit Fields5": {"main": [[{"node": "IF21", "type": "main", "index": 0}]]}, "IF21": {"main": [[{"node": "Code19", "type": "main", "index": 0}], [{"node": "Code20", "type": "main", "index": 0}]]}, "Code19": {"main": [[{"node": "Edit Fields19", "type": "main", "index": 0}]]}, "Edit Fields19": {"main": [[{"node": "IF22", "type": "main", "index": 0}]]}, "Code20": {"main": [[{"node": "Edit Fields20", "type": "main", "index": 0}]]}, "Edit Fields20": {"main": [[{"node": "IF23", "type": "main", "index": 0}]]}, "Code6": {"main": [[{"node": "Edit Fields6", "type": "main", "index": 0}]]}, "Edit Fields6": {"main": [[{"node": "IF28", "type": "main", "index": 0}]]}, "IF28": {"main": [[{"node": "Code25", "type": "main", "index": 0}], [{"node": "Code26", "type": "main", "index": 0}]]}, "Code25": {"main": [[{"node": "Edit Fields25", "type": "main", "index": 0}]]}, "Edit Fields25": {"main": [[{"node": "IF29", "type": "main", "index": 0}]]}, "Code26": {"main": [[{"node": "Edit Fields26", "type": "main", "index": 0}]]}, "Edit Fields26": {"main": [[{"node": "IF30", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "d38289e0-49d3-4e1d-8e4b-46e4eb85a2c9", "id": "iKlx4AGIjCNJSu9M", "meta": {"instanceId": "8a47b83b4479b11330fdf21ccc96d4a8117035a968612e452b4c87bfd09c16c7"}, "tags": []}