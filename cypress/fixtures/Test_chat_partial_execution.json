{"nodes": [{"parameters": {"assignments": {"assignments": [{"id": "0c345346-8cef-415c-aa1a-3d3941bb4035", "name": "text", "value": "=Set 1 with chatInput: {{ $json.chatInput }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [220, 0], "id": "b1584b5b-c17c-4fd9-9b75-dd61f2c4c20d", "name": "<PERSON>"}, {"parameters": {"assignments": {"assignments": [{"id": "9a7bd7af-c3fb-4984-b15a-2f805b66ed02", "name": "text", "value": "=Set 2 with chatInput: {{ $('When chat message received').item.json.chatInput }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [440, 0], "id": "e9e02219-4b6b-48d1-8d3d-2c850362abf2", "name": "Edit Fields1"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [0, 0], "id": "c2dd390e-1360-4d6f-a922-4d295246a886", "name": "When chat message received", "webhookId": "28da48d8-cef1-4364-b4d6-429212d2e3f6"}, {"parameters": {"assignments": {"assignments": [{"id": "9a7bd7af-c3fb-4984-b15a-2f805b66ed02", "name": "text", "value": "=Set 3 with chatInput: {{ $('When chat message received').item.json.chatInput }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [660, 0], "id": "766dba66-a4da-4d84-ad80-ca5579ce91e5", "name": "Edit Fields2"}], "connections": {"Edit Fields": {"main": [[{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "Edit Fields1": {"main": [[{"node": "Edit Fields2", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "27cc9b56542ad45b38725555722c50a1c3fee1670bbb67980558314ee08517c4"}}