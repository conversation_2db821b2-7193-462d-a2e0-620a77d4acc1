{"name": "Schedule + pinned", "nodes": [{"parameters": {"rule": {"interval": [{}]}}, "id": "66358c29-b263-43dd-be25-3b068b0a88eb", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.1, "position": [660, 340]}, {"parameters": {"options": {}}, "id": "6d903354-4e59-4032-81fe-426a5d6ec33c", "name": "<PERSON>", "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [860, 240]}, {"parameters": {"options": {}}, "id": "d8a1e9cf-81d3-400f-97d4-ad6167e7b236", "name": "Edit Fields1", "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [860, 440]}, {"parameters": {"options": {}}, "id": "bdc41148-067e-4649-8f21-5707b128d877", "name": "Edit Fields2", "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [1080, 440]}, {"parameters": {"options": {}}, "id": "d5a4337f-a6b3-4b51-9b02-e668593d9ae8", "name": "Edit Fields3", "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [1300, 440]}, {"parameters": {"options": {}}, "id": "fbc23f60-e7f6-4423-9329-33b0e4809a9a", "name": "Edit Fields4", "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [1500, 440]}, {"parameters": {"options": {}}, "id": "eaee47b0-94ec-4137-bfeb-a6c1a2c63f81", "name": "Edit Fields5", "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [1080, 240]}, {"parameters": {"options": {}}, "id": "eabb6308-21e9-4e59-8f74-9220a03c3186", "name": "Edit Fields6", "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [1300, 240]}, {"parameters": {"options": {}}, "id": "8812a45b-5545-4080-aad8-8e9f7b17ecd7", "name": "Edit Fields7", "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [1500, 240]}, {"parameters": {"options": {}}, "id": "d5ea3c5b-0b3e-4514-93e1-9c88563bab5c", "name": "Edit Fields9", "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [1700, 240]}, {"parameters": {"options": {}}, "id": "7af34474-5cd0-40b1-abea-850858e3b495", "name": "Edit Fields10", "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [1700, 440]}], "pinData": {"Schedule Trigger": [{"json": {"name": "First item", "code": 1}}, {"json": {"name": "Second item", "code": 2}}], "Edit Fields7": [{"json": {"name": "First item", "code": 1}}, {"json": {"name": "Second item", "code": 2}}], "Edit Fields2": [{"json": {"name": "First item", "code": 1}}, {"json": {"name": "Second item", "code": 2}}]}, "connections": {"Schedule Trigger": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}, {"node": "Edit Fields1", "type": "main", "index": 0}]]}, "Edit Fields1": {"main": [[{"node": "Edit Fields2", "type": "main", "index": 0}]]}, "Edit Fields2": {"main": [[{"node": "Edit Fields3", "type": "main", "index": 0}]]}, "Edit Fields3": {"main": [[{"node": "Edit Fields4", "type": "main", "index": 0}]]}, "Edit Fields5": {"main": [[{"node": "Edit Fields6", "type": "main", "index": 0}]]}, "Edit Fields6": {"main": [[{"node": "Edit Fields7", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Edit Fields5", "type": "main", "index": 0}]]}, "Edit Fields7": {"main": [[{"node": "Edit Fields9", "type": "main", "index": 0}]]}, "Edit Fields4": {"main": [[{"node": "Edit Fields10", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "9b6c68c0-f94f-45bc-a604-bf97d17a47ac", "meta": {"templateCredsSetupCompleted": true, "instanceId": "8a47b83b4479b11330fdf21ccc96d4a8117035a968612e452b4c87bfd09c16c7"}, "id": "nWzcnYUb3AVaZpHG", "tags": []}