{"meta": {"instanceId": "123"}, "nodes": [{"parameters": {"resource": "credential", "name": "123", "credentialTypeName": "123"}, "id": "a01f79f6-e8c3-44c5-be5e-4bc482e23172", "name": "n8n", "type": "n8n-nodes-base.n8n", "typeVersion": 1, "position": [540, 240], "credentials": {"n8nApi": {"id": "10", "name": "n8n account"}}}, {"parameters": {}, "id": "acdd1bdc-c642-4ea6-ad67-f4201b640cfa", "name": "When clicking ‘Execute workflow’", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [300, 240]}], "connections": {"When clicking ‘Execute workflow’": {"main": [[{"node": "n8n", "type": "main", "index": 0}]]}}}