{"name": "My workflow", "nodes": [{"parameters": {"operation": "getAllPeople", "limit": 10}, "id": "441afcbf-a678-4463-bc89-7e0b6693af5c", "name": "Customer Datastore (n8n training)", "type": "n8n-nodes-base.n8nTrainingCustomerDatastore", "typeVersion": 1, "position": [720, 440]}, {"parameters": {"values": {"number": [{"name": "objectValue.prop1", "value": 123}], "string": [{"name": "objectValue.prop2", "value": "someText"}]}, "options": {"dotNotation": true}}, "id": "44094a05-b3b7-49bf-bfbf-a711e6ba45d8", "name": "Set", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [1080, 440]}, {"parameters": {}, "id": "3dc7cf26-ff25-4437-b9fd-0e8b127ebec9", "name": "When clicking ‘Execute workflow’", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [500, 440]}], "pinData": {"Set": [{"json": {"key0": 0, "key1": 1, "key2": 2, "key3": 3, "key4": 4, "key5": 5, "key6": 6, "key7": 7, "key8": 8, "key9": 9, "key10": 10, "key11": 11, "key12": 12, "key13": 13, "key14": 14, "key15": 15, "key16": 16, "key17": 17, "key18": 18, "key19": 19}}, {"json": {"key0": 0, "key1": 1, "key2": 2, "key3": 3, "key4": 4, "key5": 5, "key6": 6, "key7": 7, "key8": 8, "key9": 9, "key10": 10, "key11": 11, "key12": 12, "key13": 13, "key14": 14, "key15": 15, "key16": 16, "key17": 17, "key18": 18, "key19": 19}}, {"json": {"key0": 0, "key1": 1, "key2": 2, "key3": 3, "key4": 4, "key5": 5, "key6": 6, "key7": 7, "key8": 8, "key9": 9, "key10": 10, "key11": 11, "key12": 12, "key13": 13, "key14": 14, "key15": 15, "key16": 16, "key17": 17, "key18": 18, "key19": 19}}, {"json": {"key0": 0, "key1": 1, "key2": 2, "key3": 3, "key4": 4, "key5": 5, "key6": 6, "key7": 7, "key8": 8, "key9": 9, "key10": 10, "key11": 11, "key12": 12, "key13": 13, "key14": 14, "key15": 15, "key16": 16, "key17": 17, "key18": 18, "key19": 19}}, {"json": {"key0": 0, "key1": 1, "key2": 2, "key3": 3, "key4": 4, "key5": 5, "key6": 6, "key7": 7, "key8": 8, "key9": 9, "key10": 10, "key11": 11, "key12": 12, "key13": 13, "key14": 14, "key15": 15, "key16": 16, "key17": 17, "key18": 18, "key19": 19}}, {"json": {"key0": 0, "key1": 1, "key2": 2, "key3": 3, "key4": 4, "key5": 5, "key6": 6, "key7": 7, "key8": 8, "key9": 9, "key10": 10, "key11": 11, "key12": 12, "key13": 13, "key14": 14, "key15": 15, "key16": 16, "key17": 17, "key18": 18, "key19": 19}}, {"json": {"key0": 0, "key1": 1, "key2": 2, "key3": 3, "key4": 4, "key5": 5, "key6": 6, "key7": 7, "key8": 8, "key9": 9, "key10": 10, "key11": 11, "key12": 12, "key13": 13, "key14": 14, "key15": 15, "key16": 16, "key17": 17, "key18": 18, "key19": 19}}, {"json": {"key0": 0, "key1": 1, "key2": 2, "key3": 3, "key4": 4, "key5": 5, "key6": 6, "key7": 7, "key8": 8, "key9": 9, "key10": 10, "key11": 11, "key12": 12, "key13": 13, "key14": 14, "key15": 15, "key16": 16, "key17": 17, "key18": 18, "key19": 19}}, {"json": {"key0": 0, "key1": 1, "key2": 2, "key3": 3, "key4": 4, "key5": 5, "key6": 6, "key7": 7, "key8": 8, "key9": 9, "key10": 10, "key11": 11, "key12": 12, "key13": 13, "key14": 14, "key15": 15, "key16": 16, "key17": 17, "key18": 18, "key19": 19}}, {"json": {"key0": 0, "key1": 1, "key2": 2, "key3": 3, "key4": 4, "key5": 5, "key6": 6, "key7": 7, "key8": 8, "key9": 9, "key10": 10, "key11": 11, "key12": 12, "key13": 13, "key14": 14, "key15": 15, "key16": 16, "key17": 17, "key18": 18, "key19": 19}}, {"json": {"key0": 0, "key1": 1, "key2": 2, "key3": 3, "key4": 4, "key5": 5, "key6": 6, "key7": 7, "key8": 8, "key9": 9, "key10": 10, "key11": 11, "key12": 12, "key13": 13, "key14": 14, "key15": 15, "key16": 16, "key17": 17, "key18": 18, "key19": 19}}, {"json": {"key0": 0, "key1": 1, "key2": 2, "key3": 3, "key4": 4, "key5": 5, "key6": 6, "key7": 7, "key8": 8, "key9": 9, "key10": 10, "key11": 11, "key12": 12, "key13": 13, "key14": 14, "key15": 15, "key16": 16, "key17": 17, "key18": 18, "key19": 19}}, {"json": {"key0": 0, "key1": 1, "key2": 2, "key3": 3, "key4": 4, "key5": 5, "key6": 6, "key7": 7, "key8": 8, "key9": 9, "key10": 10, "key11": 11, "key12": 12, "key13": 13, "key14": 14, "key15": 15, "key16": 16, "key17": 17, "key18": 18, "key19": 19}}, {"json": {"key0": 0, "key1": 1, "key2": 2, "key3": 3, "key4": 4, "key5": 5, "key6": 6, "key7": 7, "key8": 8, "key9": 9, "key10": 10, "key11": 11, "key12": 12, "key13": 13, "key14": 14, "key15": 15, "key16": 16, "key17": 17, "key18": 18, "key19": 19}}, {"json": {"key0": 0, "key1": 1, "key2": 2, "key3": 3, "key4": 4, "key5": 5, "key6": 6, "key7": 7, "key8": 8, "key9": 9, "key10": 10, "key11": 11, "key12": 12, "key13": 13, "key14": 14, "key15": 15, "key16": 16, "key17": 17, "key18": 18, "key19": 19}}, {"json": {"key0": 0, "key1": 1, "key2": 2, "key3": 3, "key4": 4, "key5": 5, "key6": 6, "key7": 7, "key8": 8, "key9": 9, "key10": 10, "key11": 11, "key12": 12, "key13": 13, "key14": 14, "key15": 15, "key16": 16, "key17": 17, "key18": 18, "key19": 19}}, {"json": {"key0": 0, "key1": 1, "key2": 2, "key3": 3, "key4": 4, "key5": 5, "key6": 6, "key7": 7, "key8": 8, "key9": 9, "key10": 10, "key11": 11, "key12": 12, "key13": 13, "key14": 14, "key15": 15, "key16": 16, "key17": 17, "key18": 18, "key19": 19}}, {"json": {"key0": 0, "key1": 1, "key2": 2, "key3": 3, "key4": 4, "key5": 5, "key6": 6, "key7": 7, "key8": 8, "key9": 9, "key10": 10, "key11": 11, "key12": 12, "key13": 13, "key14": 14, "key15": 15, "key16": 16, "key17": 17, "key18": 18, "key19": 19}}, {"json": {"key0": 0, "key1": 1, "key2": 2, "key3": 3, "key4": 4, "key5": 5, "key6": 6, "key7": 7, "key8": 8, "key9": 9, "key10": 10, "key11": 11, "key12": 12, "key13": 13, "key14": 14, "key15": 15, "key16": 16, "key17": 17, "key18": 18, "key19": 19}}, {"json": {"key0": 0, "key1": 1, "key2": 2, "key3": 3, "key4": 4, "key5": 5, "key6": 6, "key7": 7, "key8": 8, "key9": 9, "key10": 10, "key11": 11, "key12": 12, "key13": 13, "key14": 14, "key15": 15, "key16": 16, "key17": 17, "key18": 18, "key19": 19}}]}, "connections": {"Customer Datastore (n8n training)": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}, "When clicking ‘Execute workflow’": {"main": [[{"node": "Customer Datastore (n8n training)", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "versionId": "", "meta": {"instanceId": "363581be2c2581d1b11e189456a090887e137f8393a4b5cb85641b1ee4fae479"}, "tags": []}