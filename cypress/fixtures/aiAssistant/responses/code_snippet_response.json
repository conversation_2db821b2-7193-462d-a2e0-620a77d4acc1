{"sessionId": "f1d19ed5-0d55-4bad-b49a-f0c56bd6f76f-705b5dbf-12d4-4805-87a3-1e5b3c716d29-W1JgVNrpfitpSNF9rAjB4", "messages": [{"role": "assistant", "type": "message", "text": "To use expressions in n8n, follow these steps:\n\n1. Hover over the parameter where you want to use an expression.\n2. Select **Expressions** in the **Fixed/Expression** toggle.\n3. Write your expression in the parameter, or select **Open expression editor** to open the expressions editor. You can browse the available data in the **Variable selector**. All expressions have the format `{{ your expression here }}`.\n\n### Example: Get data from webhook body\n\nIf your webhook data looks like this:\n\n```json\n[\n  {\n    \"headers\": {\n      \"host\": \"n8n.instance.address\",\n      ...\n    },\n    \"params\": {},\n    \"query\": {},\n    \"body\": {\n      \"name\": \"<PERSON>\",\n      \"age\": 30,\n      \"city\": \"New York\"\n    }\n  }\n]\n```\n\nYou can use the following expression to get the value of `city`:\n\n```js\n{{$json.body.city}}\n```\n\nThis expression accesses the incoming JSON-formatted data using n8n's custom `$json` variable and finds the value of `city` (in this example, \"New York\").", "codeSnippet": "{{$json.body.city}}"}, {"role": "assistant", "type": "message", "text": "Did this answer solve your question?", "quickReplies": [{"text": "Yes, thanks", "type": "all-good", "isFeedback": true}, {"text": "No, I am still stuck", "type": "still-stuck", "isFeedback": true}]}]}