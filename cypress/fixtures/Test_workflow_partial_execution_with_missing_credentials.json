{"meta": {"templateCredsSetupCompleted": true, "instanceId": "2be09fdcb9594c0827fd4cee80f7e590c93297d9217685f34c2250fe3144ef0c"}, "nodes": [{"parameters": {}, "id": "09e4325e-ede1-40cf-a1ba-58612bbc7f1b", "name": "When clicking ‘Execute workflow’", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [820, 400]}, {"parameters": {"category": "randomData"}, "id": "4920bf3a-9978-4196-9dcb-8c2892e5641b", "name": "DebugHelper", "type": "n8n-nodes-base.debugHelper", "typeVersion": 1, "position": [1040, 400]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "7508343e-3e99-4d12-96e4-00a35a3d4306", "leftValue": "={{ $json.email }}", "rightValue": ".", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}, "options": {}}, "id": "4f6a6a4e-19b6-43f5-ba5c-e40b09d7f873", "name": "Filter", "type": "n8n-nodes-base.filter", "typeVersion": 2, "position": [1260, 400]}, {"parameters": {"chatId": "123123", "text": "1123123", "additionalFields": {}}, "id": "1765f352-fc12-4fab-9c24-d666a150266f", "name": "Telegram", "type": "n8n-nodes-base.telegram", "typeVersion": 1.1, "position": [1480, 400]}], "connections": {"When clicking ‘Execute workflow’": {"main": [[{"node": "DebugHelper", "type": "main", "index": 0}]]}, "DebugHelper": {"main": [[{"node": "Filter", "type": "main", "index": 0}]]}, "Filter": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}}, "pinData": {}}