{"nodes": [{"parameters": {"options": {}}, "id": "535fd3dd-e78f-4ffa-a085-79723fc81b38", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [320, -380], "webhookId": "4fb58136-3481-494a-a30f-d9e064dac186"}, {"parameters": {"mode": "raw", "jsonOutput": "{\n  \"this_my_field_1\": \"value\",\n  \"this_my_field_2\": 1\n}\n", "options": {}}, "id": "78201ec2-6def-40b7-85e5-97b580d7f642", "name": "Node 1", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [580, -380]}, {"parameters": {"mode": "raw", "jsonOutput": "{\n  \"this_my_field_3\": \"value\",\n  \"this_my_field_4\": 1\n}\n", "options": {}}, "id": "1cfca06d-3ec3-427f-89f7-1ef321e025ff", "name": "Node 2", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [780, -380]}], "connections": {"When chat message received": {"main": [[{"node": "Node 1", "type": "main", "index": 0}]]}, "Node 1": {"main": [[{"node": "Node 2", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "178ef8a5109fc76c716d40bcadb720c455319f7b7a3fd5a39e4f336a091f524a"}}