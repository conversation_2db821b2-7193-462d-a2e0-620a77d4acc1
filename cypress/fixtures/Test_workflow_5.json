{"meta": {"instanceId": "8147b3a74cd161276e0f3bfc17369a724afab0d377593fada8be82d34c0c6a95"}, "nodes": [{"parameters": {"jsCode": "return [\n  {\n    id: 6666\n  },\n  {\n    id: 3333\n  },\n  {\n    id: 9999\n  },\n  {\n    id: 1111\n  },\n  {\n    id: 4444\n  },\n  {\n    id: 8888\n  },\n]"}, "id": "5f023c7c-67ca-47a0-8a90-8227fcf29b9c", "name": "Code", "type": "n8n-nodes-base.code", "typeVersion": 1, "position": [-520, 580]}, {"parameters": {"values": {"string": [{"name": "id", "value": "={{ $json.id }}"}]}, "options": {}}, "id": "bd454282-9dd7-465f-9b9a-654a0c8532ec", "name": "Set2", "type": "n8n-nodes-base.set", "typeVersion": 2, "position": [-40, 780]}, {"parameters": {}, "id": "ef63cdc5-50bc-4525-9873-7e7f7589a60e", "name": "When clicking ‘Execute workflow’", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-740, 580]}, {"parameters": {"sortFieldsUi": {"sortField": [{"fieldName": "id"}]}, "options": {}}, "id": "555a150c-d735-4331-b628-c1f1cfed2da1", "name": "Sort", "type": "n8n-nodes-base.sort", "typeVersion": 1, "position": [-280, 580]}, {"parameters": {"values": {"string": [{"name": "id", "value": "={{ $json.id }}"}]}, "options": {}}, "id": "02372cb6-aac8-45c3-8600-f699901289ac", "name": "Set", "type": "n8n-nodes-base.set", "typeVersion": 2, "position": [-60, 580]}, {"parameters": {"options": {}}, "id": "00d73944-218c-4896-af68-3f2855a922d1", "name": "Set1", "type": "n8n-nodes-base.set", "typeVersion": 2, "position": [-280, 780]}, {"parameters": {"conditions": {"number": [{"value1": "={{ $json.id }}", "operation": "smallerEqual", "value2": 6666}]}}, "id": "211a7bef-32d1-4928-9cef-3a45f2e61379", "name": "IF", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [160, 580]}, {"parameters": {"options": {}}, "id": "dcbd4745-832f-43d8-8a3c-dd80e8ca2777", "name": "Set3", "type": "n8n-nodes-base.set", "typeVersion": 2, "position": [140, 780]}, {"parameters": {"jsCode": "return [\n  {\n    id: 1000\n  },\n  {\n    id: 300\n  },\n  {\n    id: 2000\n  },\n  {\n    id: 100\n  },\n  {\n    id: 400\n  },\n  {\n    id: 1300\n  },\n]"}, "id": "ec9c8f16-f3c8-4054-a6e9-4f1ebcdebb71", "name": "Code1", "type": "n8n-nodes-base.code", "typeVersion": 1, "position": [-520, 780]}, {"parameters": {"options": {}}, "id": "42e89478-a53a-4d10-b20c-1dc5d5f953d5", "name": "Set4", "type": "n8n-nodes-base.set", "typeVersion": 2, "position": [460, 460]}, {"parameters": {"options": {}}, "id": "5085eb1c-0345-4b9d-856a-2955279f2c5d", "name": "Set5", "type": "n8n-nodes-base.set", "typeVersion": 2, "position": [460, 660]}], "connections": {"Code": {"main": [[{"node": "Sort", "type": "main", "index": 0}]]}, "Set2": {"main": [[{"node": "Set3", "type": "main", "index": 0}]]}, "When clicking ‘Execute workflow’": {"main": [[{"node": "Code", "type": "main", "index": 0}, {"node": "Code1", "type": "main", "index": 0}]]}, "Sort": {"main": [[{"node": "Set", "type": "main", "index": 0}, {"node": "Set2", "type": "main", "index": 0}]]}, "Set": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}, "Set1": {"main": [[{"node": "Set2", "type": "main", "index": 0}]]}, "IF": {"main": [[{"node": "Set4", "type": "main", "index": 0}, {"node": "Set5", "type": "main", "index": 0}], [{"node": "Set5", "type": "main", "index": 0}]]}, "Code1": {"main": [[{"node": "Set1", "type": "main", "index": 0}]]}}}