import { User } from '../user';

describe('User Entity', () => {
	describe('JSON.stringify', () => {
		it('should not serialize sensitive data', () => {
			const user = Object.assign(new User(), {
				email: '<EMAIL>',
				firstName: '<PERSON>',
				lastName: '<PERSON>',
				password: '123456789',
			});
			expect(JSON.stringify(user)).toEqual(
				'{"email":"<EMAIL>","firstName":"<PERSON>","lastName":"Joe"}',
			);
		});
	});

	describe('createPersonalProjectName', () => {
		test.each([
			['<PERSON>', '<PERSON>', 'nathan@nathaniel.n8n', '<PERSON> <nathan@nathaniel.n8n>'],
			[undefined, '<PERSON>', 'nathan@nathaniel.n8n', '<nathan@nathaniel.n8n>'],
			['<PERSON>', undefined, 'nathan@nathaniel.n8n', '<nathan@nathaniel.n8n>'],
			[undefined, undefined, 'nathan@nathaniel.n8n', '<nathan@nathaniel.n8n>'],
			[undefined, undefined, undefined, 'Unnamed Project'],
			['<PERSON>', '<PERSON>', undefined, 'Unnamed Project'],
		])(
			'given fistName: %s, lastName: %s and email: %s this gives the projectName: "%s"',
			async (firstName, lastName, email, projectName) => {
				const user = new User();
				Object.assign(user, { firstName, lastName, email });
				expect(user.createPersonalProjectName()).toBe(projectName);
			},
		);
	});
});
