services:
  mockapi:
    image: wiremock/wiremock:3.9.1
    ports:
      - '8088:8080'
    volumes:
      - ${MOCK_API_DATA_PATH}/mappings:/home/<USER>/mappings

  redis:
    image: redis:6.2.14-alpine
    ports:
      - 6379:6379
    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']
      interval: 1s
      timeout: 3s

  postgres:
    image: postgres:16.4
    user: root:root
    restart: always
    environment:
      - POSTGRES_DB=n8n
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
      - PGDATA=/var/lib/postgresql/data/pgdata
    volumes:
      - ${RUN_DIR}/postgres:/var/lib/postgresql/data
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U postgres']
      interval: 5s
      timeout: 5s
      retries: 10

  n8n_worker1:
    image: ghcr.io/n8n-io/n8n:${N8N_VERSION:-latest}
    user: root:root
    environment:
      - N8N_DIAGNOSTICS_ENABLED=false
      - N8N_USER_FOLDER=/n8n/worker1
      - N8N_ENCRYPTION_KEY=${N8N_ENCRYPTION_KEY}
      # Queue mode config
      - EXECUTIONS_MODE=queue
      - QUEUE_BULL_REDIS_HOST=redis
      - QUEUE_HEALTH_CHECK_ACTIVE=true
      - N8N_CONCURRENCY_PRODUCTION_LIMIT=10
      # DB config
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=postgres
      - DB_POSTGRESDB_PASSWORD=password
      # Task Runner config
      - N8N_RUNNERS_ENABLED=true
      - N8N_RUNNERS_MODE=internal
      # Disable Insights
      - N8N_DISABLED_MODULES=insights
    command: worker
    volumes:
      - ${RUN_DIR}/n8n-worker1:/n8n
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ['CMD-SHELL', 'wget --spider -q http://n8n_worker1:5678/healthz || exit 1']
      interval: 5s
      timeout: 5s
      retries: 10

  n8n_worker2:
    image: ghcr.io/n8n-io/n8n:${N8N_VERSION:-latest}
    user: root:root
    environment:
      - N8N_DIAGNOSTICS_ENABLED=false
      - N8N_USER_FOLDER=/n8n/worker2
      - N8N_ENCRYPTION_KEY=${N8N_ENCRYPTION_KEY}
      # Queue mode config
      - EXECUTIONS_MODE=queue
      - QUEUE_BULL_REDIS_HOST=redis
      - QUEUE_HEALTH_CHECK_ACTIVE=true
      - N8N_CONCURRENCY_PRODUCTION_LIMIT=10
      # DB config
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=postgres
      - DB_POSTGRESDB_PASSWORD=password
      # Task Runner config
      - N8N_RUNNERS_ENABLED=true
      - N8N_RUNNERS_MODE=internal
      # Disable Insights
      - N8N_DISABLED_MODULES=insights
    command: worker
    volumes:
      - ${RUN_DIR}/n8n-worker2:/n8n
    depends_on:
      # We let the worker 1 start first so it can run the DB migrations
      n8n_worker1:
        condition: service_healthy
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ['CMD-SHELL', 'wget --spider -q http://n8n_worker2:5678/healthz || exit 1']
      interval: 5s
      timeout: 5s
      retries: 10

  n8n:
    image: ghcr.io/n8n-io/n8n:${N8N_VERSION:-latest}
    user: root:root
    environment:
      - N8N_DIAGNOSTICS_ENABLED=false
      - N8N_USER_FOLDER=/n8n/main
      - N8N_ENCRYPTION_KEY=${N8N_ENCRYPTION_KEY}
      # Queue mode config
      - EXECUTIONS_MODE=queue
      - QUEUE_BULL_REDIS_HOST=redis
      # DB config
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=postgres
      - DB_POSTGRESDB_PASSWORD=password
      # Task Runner config
      - N8N_RUNNERS_ENABLED=true
      - N8N_RUNNERS_MODE=internal
      # Disable Insights
      - N8N_DISABLED_MODULES=insights
    ports:
      - 5678:5678
    volumes:
      - ${RUN_DIR}/n8n-main:/n8n
    depends_on:
      n8n_worker1:
        condition: service_healthy
      n8n_worker2:
        condition: service_healthy
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      mockapi:
        condition: service_started
    healthcheck:
      test: ['CMD-SHELL', 'wget --spider -q http://n8n:5678/healthz || exit 1']
      interval: 5s
      timeout: 5s
      retries: 10

  benchmark:
    image: ghcr.io/n8n-io/n8n-benchmark:${N8N_BENCHMARK_VERSION:-latest}
    depends_on:
      n8n:
        condition: service_healthy
    environment:
      - N8N_BASE_URL=http://n8n:5678
      - K6_API_TOKEN=${K6_API_TOKEN}
      - BENCHMARK_RESULT_WEBHOOK_URL=${BENCHMARK_RESULT_WEBHOOK_URL}
      - BENCHMARK_RESULT_WEBHOOK_AUTH_HEADER=${BENCHMARK_RESULT_WEBHOOK_AUTH_HEADER}
