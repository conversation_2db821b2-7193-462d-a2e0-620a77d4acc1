{"createdAt": "2024-09-03T11:51:56.540Z", "updatedAt": "2024-09-03T12:22:21.000Z", "name": "Binary Data", "active": true, "nodes": [{"parameters": {"httpMethod": "POST", "path": "binary-files-benchmark", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [0, 0], "id": "bfe19f12-3655-440f-be5c-8d71665c6353", "name": "Webhook", "webhookId": "109d7b13-93ad-42b0-a9ce-ca49e1817b35"}, {"parameters": {"respondWith": "binary", "options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [740, 0], "id": "cd957c9b-6b7a-4423-aac3-6df4d8bb571e", "name": "Respond to Webhook"}, {"parameters": {"operation": "write", "fileName": "=file-{{ Date.now() }}-{{ Math.random() }}.js", "dataPropertyName": "file", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [260, 0], "id": "f2ce4709-7697-4bc6-8eca-6c222485297a", "name": "Write File to Disk"}, {"parameters": {"fileSelector": "={{ $json.fileName }}", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [500, 0], "id": "198e8a6c-81a3-4b34-b099-501961a02006", "name": "Read File from Disk"}], "connections": {"Webhook": {"main": [[{"node": "Write File to Disk", "type": "main", "index": 0}]]}, "Write File to Disk": {"main": [[{"node": "Read File from Disk", "type": "main", "index": 0}]]}, "Read File from Disk": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "meta": null, "pinData": {}, "versionId": "8dd197c0-d1ea-43c3-9f88-9d11e7b081a0", "triggerCount": 1, "tags": []}