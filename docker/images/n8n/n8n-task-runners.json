{"task-runners": [{"runner-type": "javascript", "workdir": "/home/<USER>", "command": "/usr/local/bin/node", "args": ["--disallow-code-generation-from-strings", "--disable-proto=delete", "/usr/local/lib/node_modules/n8n/node_modules/@n8n/task-runner/dist/start.js"], "allowed-env": ["PATH", "GENERIC_TIMEZONE", "N8N_RUNNERS_GRANT_TOKEN", "N8N_RUNNERS_TASK_BROKER_URI", "N8N_RUNNERS_MAX_PAYLOAD", "N8N_RUNNERS_MAX_CONCURRENCY", "N8N_RUNNERS_TASK_TIMEOUT", "N8N_RUNNERS_HEARTBEAT_INTERVAL", "N8N_RUNNERS_HEALTH_CHECK_SERVER_ENABLED", "N8N_RUNNERS_HEALTH_CHECK_SERVER_HOST", "N8N_RUNNERS_HEALTH_CHECK_SERVER_PORT", "NODE_FUNCTION_ALLOW_BUILTIN", "NODE_FUNCTION_ALLOW_EXTERNAL", "N8N_RUNNERS_ALLOW_PROTOTYPE_MUTATION", "NODE_OPTIONS", "NODE_PATH", "N8N_SENTRY_DSN", "N8N_VERSION", "ENVIRONMENT", "DEPLOYMENT_NAME"]}]}